{"name": "@workflow-mapper/spec-parser-lib", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"gray-matter": "^4.0.3", "yaml": "^2.3.2", "uuid": "^9.0.0"}, "devDependencies": {"@types/uuid": "^10.0.0", "typescript": "5.4.3", "tsup": "8.0.2", "vitest": "3.1.4"}}
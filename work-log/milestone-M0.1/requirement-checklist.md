---
title: "Requirement Checklist: Milestone M0.1 — Knowledge-Graph Bootstrap"
milestone: "milestone-M0.1"
agent: "augment"
created: "2025-01-25"
status: "In Progress"
---

# Milestone M0.1 — Knowledge-Graph Bootstrap - Requirement Checklist

## 🎯 Definition of Done

- [ ] **DOD-1** `pnpm run build-kg` scans all `docs/tech-specs/**/*.mdx` and writes `kg.jsonld` and `kg.yaml` in repo root
- [ ] **DOD-2** CLI supports `--dry-run` (prints summary, no file write)
- [ ] **DOD-3** CI job `graph-build` passes on push & PR
- [ ] **DOD-4** Every MDX spec contains a resolvable `@id` in the graph
- [ ] **DOD-5** Spec passes `spec-lint` and dry-run gates

## ✅ Success Criteria

- [ ] **SC-1** `pnpm run build-kg -- --dry-run docs/tech-specs` exits 0
- [ ] **SC-2** Running without `--dry-run` writes both graph files
- [ ] **SC-3** CI `graph.yml` job passes on PR & push
- [ ] **SC-4** `kg.yaml` shows at least: one milestone node (M0), one component node, one implements edge
- [ ] **SC-5** Spec passes checklist lint: `node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx`
- [ ] **SC-6** Agent dry-run passes: `pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx`

## 📦 Deliverables

### Package: spec-parser-lib
- [ ] **DEL-1** Create `code/packages/spec-parser-lib/` directory
- [ ] **DEL-2** Implement `parse-specs.ts` with MDX front-matter parsing
- [ ] **DEL-3** Add comprehensive tests for parser functionality
- [ ] **DEL-4** Create `package.json` following existing patterns
- [ ] **DEL-5** Configure TypeScript build with tsup
- [ ] **DEL-6** Set up vitest for testing

### Package: kg-cli
- [ ] **DEL-7** Create `code/packages/kg-cli/` directory
- [ ] **DEL-8** Implement `build-kg.ts` CLI tool
- [ ] **DEL-9** Support `--dry-run` flag
- [ ] **DEL-10** Generate both JSON-LD and YAML outputs
- [ ] **DEL-11** Add comprehensive tests for CLI
- [ ] **DEL-12** Create `package.json` following existing patterns

### Schema and Configuration
- [ ] **DEL-13** Create `kg-schema.yml` in repo root
- [ ] **DEL-14** Define entities and relationships structure
- [ ] **DEL-15** Ensure schema supports milestones, components, and relationships

### CI/CD Integration
- [ ] **DEL-16** Create `.github/workflows/graph.yml`
- [ ] **DEL-17** Configure CI to run `pnpm run build-kg -- --dry-run docs/tech-specs`
- [ ] **DEL-18** Ensure CI passes on both push and PR

### Editor Support
- [ ] **DEL-19** Create/update `.vscode/extensions.json`
- [ ] **DEL-20** Recommend MDX and Markdown preview extensions
- [ ] **DEL-21** Update `docs/README.md` with VS Code/Obsidian preview instructions

### Root Package Configuration
- [ ] **DEL-22** Add `build-kg` script to root `package.json`
- [ ] **DEL-23** Ensure script works with workspace packages

## 🔧 Technical Requirements

### Dependencies
- [ ] **TECH-1** Install gray-matter@4.1.0 for front-matter parsing
- [ ] **TECH-2** Install yaml@2.3.2 for YAML output
- [ ] **TECH-3** Install uuid@9.0.0 for deterministic IDs
- [ ] **TECH-4** Use TypeScript 5.4.3 for all packages
- [ ] **TECH-5** Use existing build tools (tsup, vitest)

### Package Structure
- [ ] **TECH-6** Follow existing package naming: `@workflow-mapper/spec-parser-lib`
- [ ] **TECH-7** Follow existing package naming: `@workflow-mapper/kg-cli`
- [ ] **TECH-8** Use ESM modules (`"type": "module"`)
- [ ] **TECH-9** Generate TypeScript declarations
- [ ] **TECH-10** Follow existing test coverage thresholds

### Code Quality
- [ ] **TECH-11** Pass ESLint with existing configuration
- [ ] **TECH-12** Pass TypeScript type checking
- [ ] **TECH-13** Achieve minimum test coverage (80% unit, 70% integration)
- [ ] **TECH-14** Follow existing code patterns and conventions

## 🧪 Testing Requirements

### Unit Tests
- [ ] **TEST-1** Test MDX front-matter parsing
- [ ] **TEST-2** Test YAML and JSON-LD generation
- [ ] **TEST-3** Test CLI argument parsing
- [ ] **TEST-4** Test dry-run functionality
- [ ] **TEST-5** Test error handling and edge cases

### Integration Tests
- [ ] **TEST-6** Test full pipeline: MDX → parsing → graph generation
- [ ] **TEST-7** Test CLI with real MDX files
- [ ] **TEST-8** Test workspace integration

### Acceptance Tests
- [ ] **TEST-9** Verify all acceptance test commands pass
- [ ] **TEST-10** Test CI pipeline integration
- [ ] **TEST-11** Validate generated graph structure

## 📋 Process Requirements

### Git Workflow
- [ ] **PROC-1** Create milestone branch: `milestone/m0.1-knowledge-graph-bootstrap`
- [ ] **PROC-2** Create task branches following pattern: `m0.1/task-{##}-{description}`
- [ ] **PROC-3** Use conventional commit messages
- [ ] **PROC-4** Squash commits when merging to milestone branch

### Documentation
- [ ] **PROC-5** Update work logs in real-time (max 15min lag)
- [ ] **PROC-6** Document all technical decisions
- [ ] **PROC-7** Update execution log with progress
- [ ] **PROC-8** Create comprehensive implementation notes

### Validation
- [ ] **PROC-9** Run spec-lint before completion
- [ ] **PROC-10** Execute acceptance tests immediately after implementation
- [ ] **PROC-11** Validate all success criteria
- [ ] **PROC-12** Ensure no regressions in existing functionality

## 🚨 Risk Mitigation

### Technical Risks
- [ ] **RISK-1** Verify gray-matter compatibility with existing MDX files
- [ ] **RISK-2** Ensure YAML output is valid and parseable
- [ ] **RISK-3** Test CLI with various MDX file structures
- [ ] **RISK-4** Validate workspace package resolution

### Process Risks
- [ ] **RISK-5** Ensure CI doesn't break existing workflows
- [ ] **RISK-6** Verify package manager commands work correctly
- [ ] **RISK-7** Test integration with existing build pipeline

---

**Checklist Owner**: Augment Agent
**Review Frequency**: Real-time during implementation
**Last Updated**: 2025-01-25
